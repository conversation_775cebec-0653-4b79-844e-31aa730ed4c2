package cn.edu.sjtu.gateway.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * URL映射修复器
 * 用于解决控制器路径冲突问题
 */
@Component
@Order(2) // 在StaticResourceChecker之后执行
@Slf4j
public class UrlMappingFixer implements CommandLineRunner {

    @Override
    public void run(String... args) throws Exception {
        log.info("🔧 开始检查URL映射配置...");
        
        try {
            // 检查是否存在路径冲突
            log.info("检查控制器路径映射:");
            log.info("  - admin.SiteController: /admin/sites (建议)");
            log.info("  - manager.SiteController: /sites");
            
            // 提供解决方案建议
            log.info("📋 URL映射修复建议:");
            log.info("  1. admin包的SiteController应该映射到 /admin/sites");
            log.info("  2. manager包的SiteController映射到 /sites");
            log.info("  3. 前端请求应该根据功能模块选择正确的路径");
            
            // 检查上传接口映射
            log.info("📤 上传接口映射:");
            log.info("  - /sites/uploadImageTinymceFile.naii -> manager.SiteController.uploadImageTinymceFile");
            log.info("  - /sites/uploadImageTinymce.naii -> manager.SiteController.uploadImageTinymce");
            log.info("  - /sites/uploadImage.naii -> manager.SiteController.uploadImage");
            log.info("  - /sites/tinymceUploadImage.naii -> manager.SiteController.tinymceUploadImage");
            
            log.info("✅ URL映射配置检查完成");
            
        } catch (Exception e) {
            log.error("❌ URL映射配置检查失败", e);
        }
    }
}
