# NAII Gateway 主配置文件
# 包含所有环境的公共配置，环境特定配置在对应的 application-{profile}.yml 中

spring:
  profiles:
    active: ${NAII_GATEWAY_PROFILE:dev}

  # 数据源公共配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 数据库连接的登录账号
    username: ${NAII_DATABASES_USERNAME:root}
    # 数据库连接的登录密码
    password: ${NAII_DATABASES_PASSWORD:Passw0rd@_}

  # JPA公共配置
  jpa:
    properties:
      hibernate:
        # 明确指定MySQL方言，解决DialectResolutionInfo错误
        dialect: org.hibernate.dialect.MySQL8Dialect
        # 暂时关闭二级缓存，避免依赖问题
        cache:
          use_second_level_cache: false
          use_query_cache: false

  # Spring Boot 2.6+ 兼容性配置
  main:
    # 允许循环依赖
    allow-circular-references: true

  # MVC公共配置
  mvc:
    # 静态资源配置 - 移除view配置避免与Java配置冲突
    static-path-pattern: /static/**

  # 文件上传公共配置
  servlet:
    multipart:
      # 文件大小阈值，超过此大小将写入磁盘
      file-size-threshold: 2MB

# 服务器公共配置
server:
  max-http-header-size: ${NAII_HEADER_SIZE:10MB}
  port: ${NAII_SERVER_PORT:8080}
  tomcat:
    max-http-form-post-size: ${NAII_POST_SIZE:10MB}
    connection-timeout: ${NAII_CONNECTION_TIMEOUT:60000}
  # JSP支持配置 - JAR包模式
  jsp-servlet:
    init-parameters:
      compilerSourceVM: 17
      compilerTargetVM: 17

# API公共配置
api:
  suffix: .json

# 数据库公共配置
database:
  # 数据库的名字，数据库名
  name: ${NAII_DATABASES_NAME:naii}

# URL公共配置
url:
  # 访问的后缀名
  suffix: .naii

# 文件上传公共配置
fileupload:
  allowUploadSuffix: png|jpg|jpeg|gif|bmp|flv|swf|mkv|avi|rm|rmvb|mpeg|mpg|ogg|ogv|mov|wmv|mp4|webm|mp3|wav|mid|rar|zip|tar|gz|7z|bz2|cab|iso|doc|docx|xls|xlsx|ppt|pptx|pdf|txt|md|xml
  storage:
    local:
      # 统一文件上传路径配置
      path: ./uploads/

# VM配置 - 修复配置键名不一致问题
vm:
  showsql: ${VM_SHOW_SQL:true}
# 兼容旧配置
wm:
  showsql: ${VM_SHOW_SQL:true}

# NAII 自定义配置公共部分
naii:
  request:
    frequency:
      # 拦截过滤的请求后缀名，用这个后缀的请求，都是在被拦截保护的请求
      suffix: jsp,naii,json,html
      ip:
        delay: 1000