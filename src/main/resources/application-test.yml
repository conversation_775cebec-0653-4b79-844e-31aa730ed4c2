# NAII Gateway 测试环境配置
# 只包含测试环境特定的配置项，公共配置在 application.yml 中

database:
  # 数据库所在ip地址，连接的ip，如 *************
  ip: ${NAII_DATABASES_IP:*************}

# 日志配置 - 测试环境
logging:
  level:
    cn:
      edu:
        sjtu: ${NAII_LOGGING_SJTU_LEVEL:info}
    root: ${NAII_LOGGING_LEVEL:warn}
    sql: ${NAII_LOGGING_SQL_LEVEL:warn}
    web: ${NAII_LOGGING_WEB_LEVEL:warn}
    org:
      springframework: warn
      hibernate: warn

# 服务器配置 - 测试环境特定配置
server:
  tomcat:
    # 测试环境中等连接数配置
    max-connections: 500
    threads:
      max: 150
      min-spare: 20
  # JSP支持配置 - 测试环境
  jsp-servlet:
    init-parameters:
      development: true
spring:
  datasource:
    hikari:
      # 测试环境连接池配置 - 中等规模配置
      maximum-pool-size: 15
      minimum-idle: 8
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      # 连接池名称，便于监控
      pool-name: NaiiTestHikariCP
    # MySQL 数据库配置 - 测试环境
    url: jdbc:mysql://${database.ip}:3307/${database.name}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&rewriteBatchedStatements=true
  jpa:
    properties:
      hibernate:
        hbm2ddl:
          # 使用update模式，允许Hibernate自动修正表结构差异
          auto: update
        # 测试环境适度的SQL优化
        format_sql: false
        use_sql_comments: false
    # 测试环境不显示SQL
    show-sql: false
  servlet:
    multipart:
      # 文件上传限制 - 测试环境配置
      max-file-size: ${NAII_FILE_SIZE:200MB}
      max-request-size: ${NAII_REQUEST_SIZE:200MB}
      # 临时文件存储位置
      location: ${java.io.tmpdir}/naii-upload

# 文件上传配置 - 测试环境特定配置
fileupload:
  domain: http://*************:8080/
  maxSize: 200MB

# VM配置 - 测试环境覆盖
vm:
  showsql: false
wm:
  showsql: false

# NAII 自定义配置 - 测试环境特定配置
naii:
  request:
    frequency:
      url:
        # 测试环境适度限制URL访问频率
        delay: 200
      ip:
        # 测试环境中等IP请求限制
        requestNumber: 50
        # 触发禁止访问保护的持续时间（毫秒）- 测试环境设为15分钟
      forbidTime: 900000

# 测试环境监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
