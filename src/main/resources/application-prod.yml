# NAII Gateway 生产环境配置
# 只包含生产环境特定的配置项，公共配置在 application.yml 中

database:
  # 数据库所在ip地址，连接的ip，如 127.0.0.1
  ip: ${NAII_DATABASES_IP:127.0.0.1}

# 日志配置 - 生产环境
logging:
  level:
    cn:
      edu:
        sjtu: ${NAII_LOGGING_SJTU_LEVEL:info}
    root: ${NAII_LOGGING_LEVEL:warn}
    sql: ${NAII_LOGGING_SQL_LEVEL:error}
    web: ${NAII_LOGGING_WEB_LEVEL:error}
    org:
      springframework: error
      hibernate: error
  # 生产环境日志文件配置
  file:
    name: /var/log/naii-gateway/application.log
    max-size: 100MB
    max-history: 30

# 服务器配置 - 生产环境高性能配置
server:
  tomcat:
    # 生产环境连接超时设置
    connection-timeout: 120000
    # 生产环境高并发连接数配置
    max-connections: 10000
    threads:
      max: 300
      min-spare: 50
    # 处理大文件的配置
    max-swallow-size: 500MB
    # 生产环境性能优化
    accept-count: 1000
    max-keep-alive-requests: 1000
  # JSP支持配置 - 生产环境
  jsp-servlet:
    init-parameters:
      development: false
spring:
  datasource:
    hikari:
      # 生产环境连接池配置 - 高性能配置
      maximum-pool-size: 30
      minimum-idle: 15
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      # 生产环境连接池优化
      pool-name: NaiiProdHikariCP
      connection-test-query: SELECT 1
      validation-timeout: 5000
    # MySQL 数据库配置 - 生产环境安全配置
    url: jdbc:mysql://${database.ip}:3306/${database.name}?useUnicode=true&characterEncoding=utf-8&useSSL=true&requireSSL=true&verifyServerCertificate=false&serverTimezone=Asia/Shanghai&rewriteBatchedStatements=true&cachePrepStmts=true&prepStmtCacheSize=250&prepStmtCacheSqlLimit=2048
    # 生产环境使用不同的默认密码
    password: ${NAII_DATABASES_PASSWORD:naiiPassw0rd@_}
  jpa:
    properties:
      hibernate:
        hbm2ddl:
          # 生产环境建议使用validate或none，避免自动修改表结构
          auto: none
        # 生产环境性能优化
        format_sql: false
        use_sql_comments: false
        generate_statistics: false
        # 批处理优化
        jdbc:
          batch_size: 50
          order_inserts: true
          order_updates: true
    # 生产环境不显示SQL
    show-sql: false
  servlet:
    multipart:
      # 文件上传限制 - 生产环境大文件支持
      max-file-size: ${NAII_FILE_SIZE:500MB}
      max-request-size: ${NAII_REQUEST_SIZE:500MB}
      # 临时文件存储位置
      location: /tmp/naii-upload


url:
  # 访问的后缀名
  suffix: .naii

# 文件上传配置 - 生产环境
fileupload:
  allowUploadSuffix: png|jpg|jpeg|gif|bmp|flv|swf|mkv|avi|rm|rmvb|mpeg|mpg|ogg|ogv|mov|wmv|mp4|webm|mp3|wav|mid|rar|zip|tar|gz|7z|bz2|cab|iso|doc|docx|xls|xlsx|ppt|pptx|pdf|txt|md|xml
  domain: https://naii.sjtu.edu.cn/
  maxSize: 500MB
  storage:
    local:
      # 统一文件上传路径配置
      path: ./uploads/

# VM配置 - 修复配置键名不一致问题
vm:
  showsql: false
# 兼容旧配置
wm:
  showsql: false
# NAII 自定义配置 - 生产环境
naii:
  request:
    frequency:
      # 拦截过滤的请求后缀名，用这个后缀的请求，都是在被拦截保护的请求
      suffix: jsp,naii,json,html
      url:
        # 生产环境启用URL访问频率限制，防止恶意请求
        delay: 500
      ip:
        # 生产环境严格的IP请求限制
        requestNumber: 30
        delay: 1000
        # 触发禁止访问保护的持续时间（毫秒）- 生产环境设为30分钟
      forbidTime: 1800000

# 生产环境监控和管理配置
management:
  endpoints:
    web:
      exposure:
        # 生产环境只暴露必要的端点
        include: health,info
      base-path: /actuator
  endpoint:
    health:
      show-details: never
  server:
    port: 8081

# 生产环境安全配置
security:
  require-ssl: true