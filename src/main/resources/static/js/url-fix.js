/**
 * URL修复工具
 * 用于解决URL编码和路径问题
 */
(function() {
    'use strict';
    
    // 修复URL编码问题
    function fixUrlEncoding() {
        // 检查当前URL是否包含编码的大括号
        var currentUrl = window.location.href;
        if (currentUrl.includes('%7B') || currentUrl.includes('%7D')) {
            console.warn('检测到URL编码问题:', currentUrl);
            
            // 解码URL
            var decodedUrl = decodeURIComponent(currentUrl);
            console.log('修复后的URL:', decodedUrl);
            
            // 如果URL包含{value}这样的模板变量，需要特殊处理
            if (decodedUrl.includes('{value}')) {
                console.error('URL包含未替换的模板变量: {value}');
                // 可以重定向到首页或显示错误页面
                // window.location.href = '/';
            }
        }
    }
    
    // 修复上传接口路径
    function fixUploadPaths() {
        // 检查页面中的上传表单
        var uploadForms = document.querySelectorAll('form[action*="upload"]');
        uploadForms.forEach(function(form) {
            var action = form.getAttribute('action');
            console.log('检查上传表单action:', action);
            
            // 确保上传接口使用正确的路径
            if (action && action.includes('uploadImageTinymceFile')) {
                if (!action.startsWith('/sites/')) {
                    var newAction = '/sites/' + action.replace(/^\/+/, '');
                    form.setAttribute('action', newAction);
                    console.log('修复上传表单action:', action, '->', newAction);
                }
            }
        });
        
        // 检查AJAX上传请求
        if (window.jQuery) {
            var originalAjax = jQuery.ajax;
            jQuery.ajax = function(options) {
                if (options.url && options.url.includes('upload')) {
                    console.log('检查AJAX上传请求:', options.url);
                    
                    // 确保上传接口使用正确的路径
                    if (options.url.includes('uploadImageTinymceFile') && !options.url.startsWith('/sites/')) {
                        options.url = '/sites/' + options.url.replace(/^\/+/, '');
                        console.log('修复AJAX上传URL:', options.url);
                    }
                }
                return originalAjax.call(this, options);
            };
        }
    }
    
    // 页面加载完成后执行修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            fixUrlEncoding();
            fixUploadPaths();
        });
    } else {
        fixUrlEncoding();
        fixUploadPaths();
    }
    
    // 导出修复函数供外部调用
    window.UrlFixer = {
        fixUrlEncoding: fixUrlEncoding,
        fixUploadPaths: fixUploadPaths
    };
    
})();
