<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"], button {
            padding: 8px 12px;
            margin: 5px 0;
        }
        button {
            background-color: #007cba;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #005a87;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            min-height: 20px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <h1>🧪 上传功能测试页面</h1>
    
    <div class="test-section">
        <h3>📋 配置信息检查</h3>
        <button onclick="checkConfig()">检查静态资源配置</button>
        <button onclick="checkUploadAccess()">检查上传目录访问</button>
        <div id="configResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>📤 TinyMCE文件上传测试</h3>
        <form id="tinymceUploadForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="tinymceFile">选择文件:</label>
                <input type="file" id="tinymceFile" name="file" accept="image/*">
            </div>
            <div class="form-group">
                <label for="fileSize">文件大小限制(MB):</label>
                <input type="number" id="fileSize" name="size" value="10" min="1" max="100">
            </div>
            <div class="form-group">
                <label for="fileExts">允许的扩展名:</label>
                <input type="text" id="fileExts" name="exts" value=".jpg,.jpeg,.png,.gif,.bmp">
            </div>
            <button type="button" onclick="uploadTinymceFile()">上传文件</button>
        </form>
        <div id="tinymceResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>🖼️ 图片上传测试</h3>
        <form id="imageUploadForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="imageFile">选择图片:</label>
                <input type="file" id="imageFile" name="image" accept="image/*">
            </div>
            <button type="button" onclick="uploadImage()">上传图片</button>
        </form>
        <div id="imageResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>📊 测试结果</h3>
        <div id="testSummary" class="result info">
            点击上方按钮开始测试...
        </div>
    </div>

    <script src="/static/js/url-fix.js"></script>
    <script>
        let testResults = {
            config: false,
            uploadAccess: false,
            tinymceUpload: false,
            imageUpload: false
        };

        function updateTestSummary() {
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(r => r).length;
            const summary = document.getElementById('testSummary');
            
            summary.innerHTML = `
                <strong>测试进度: ${passed}/${total}</strong><br>
                ✅ 配置检查: ${testResults.config ? '通过' : '待测试'}<br>
                ✅ 上传目录访问: ${testResults.uploadAccess ? '通过' : '待测试'}<br>
                ✅ TinyMCE上传: ${testResults.tinymceUpload ? '通过' : '待测试'}<br>
                ✅ 图片上传: ${testResults.imageUpload ? '通过' : '待测试'}
            `;
            
            if (passed === total) {
                summary.className = 'result success';
                summary.innerHTML += '<br><strong>🎉 所有测试通过！</strong>';
            }
        }

        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            element.className = `result ${success ? 'success' : 'error'}`;
            element.innerHTML = `
                <strong>${success ? '✅ 成功' : '❌ 失败'}</strong><br>
                ${message}
                ${data ? `<br><pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
        }

        function checkConfig() {
            fetch('/test/static-config')
                .then(response => response.json())
                .then(data => {
                    testResults.config = data.success;
                    showResult('configResult', data.success, 
                        data.success ? '静态资源配置正常' : data.error || '配置检查失败', data);
                    updateTestSummary();
                })
                .catch(error => {
                    testResults.config = false;
                    showResult('configResult', false, '请求失败: ' + error.message);
                    updateTestSummary();
                });
        }

        function checkUploadAccess() {
            fetch('/test/upload-access')
                .then(response => response.json())
                .then(data => {
                    testResults.uploadAccess = data.success;
                    showResult('configResult', data.success, 
                        data.success ? '上传目录访问正常' : data.error || '上传目录访问失败', data);
                    updateTestSummary();
                })
                .catch(error => {
                    testResults.uploadAccess = false;
                    showResult('configResult', false, '请求失败: ' + error.message);
                    updateTestSummary();
                });
        }

        function uploadTinymceFile() {
            const form = document.getElementById('tinymceUploadForm');
            const formData = new FormData();
            
            const fileInput = document.getElementById('tinymceFile');
            const sizeInput = document.getElementById('fileSize');
            const extsInput = document.getElementById('fileExts');
            
            if (!fileInput.files[0]) {
                showResult('tinymceResult', false, '请选择要上传的文件');
                return;
            }
            
            formData.append('file', fileInput.files[0]);
            formData.append('size', sizeInput.value);
            formData.append('exts', extsInput.value);
            formData.append('accept', 'image/*');
            
            fetch('/sites/uploadImageTinymceFile.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                testResults.tinymceUpload = data.result === 1; // SUCCESS = 1
                showResult('tinymceResult', testResults.tinymceUpload, 
                    testResults.tinymceUpload ? `文件上传成功: ${data.path}` : data.info || '上传失败', data);
                updateTestSummary();
            })
            .catch(error => {
                testResults.tinymceUpload = false;
                showResult('tinymceResult', false, '上传请求失败: ' + error.message);
                updateTestSummary();
            });
        }

        function uploadImage() {
            const fileInput = document.getElementById('imageFile');
            
            if (!fileInput.files[0]) {
                showResult('imageResult', false, '请选择要上传的图片');
                return;
            }
            
            const formData = new FormData();
            formData.append('image', fileInput.files[0]);
            
            fetch('/sites/uploadImage.naii', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                testResults.imageUpload = data.result === 1; // SUCCESS = 1
                showResult('imageResult', testResults.imageUpload, 
                    testResults.imageUpload ? `图片上传成功: ${data.path}` : data.info || '上传失败', data);
                updateTestSummary();
            })
            .catch(error => {
                testResults.imageUpload = false;
                showResult('imageResult', false, '上传请求失败: ' + error.message);
                updateTestSummary();
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTestSummary();
            console.log('上传功能测试页面已加载');
        });
    </script>
</body>
</html>
