# NAII Gateway 开发环境配置
# 只包含开发环境特定的配置项，公共配置在 application.yml 中

database:
  # 数据库所在ip地址，连接的ip，如 127.0.0.1
  ip: ${NAII_DATABASES_IP:127.0.0.1}

# 日志配置 - 开发环境详细日志
logging:
  level:
    cn:
      edu:
        sjtu: ${NAII_LOGGING_SJTU_LEVEL:debug}
    root: ${NAII_LOGGING_LEVEL:info}
    sql: ${NAII_LOGGING_SQL_LEVEL:debug}
    web: ${NAII_LOGGING_WEB_LEVEL:debug}
    org:
      springframework:
        web: debug
      hibernate:
        SQL: debug
        type:
          descriptor:
            sql:
              BasicBinder: trace

# 服务器配置 - 开发环境特定配置
server:
  tomcat:
    # 开发环境适中的连接数配置
    max-connections: 200
    threads:
      max: 200
      min-spare: 10
  # JSP支持配置 - 开发环境
  jsp-servlet:
    init-parameters:
      development: true

spring:
  datasource:
    hikari:
      # 开发环境连接池配置 - 适中配置便于调试
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      # 连接池名称，便于监控
      pool-name: NaiiDevHikariCP
    # MySQL 数据库配置 - 开发环境优化
    url: jdbc:mysql://${database.ip}:3306/${database.name}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&rewriteBatchedStatements=true
  jpa:
    properties:
      hibernate:
        hbm2ddl:
          auto: update
        # 开发环境SQL格式化和统计
        format_sql: true
        use_sql_comments: true
        generate_statistics: true
    # 开发环境显示SQL便于调试
    show-sql: true
  main:
    # 开发环境允许Bean覆盖
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      # 文件上传限制 - 开发环境适中配置
      max-file-size: ${NAII_FILE_SIZE:100MB}
      max-request-size: ${NAII_REQUEST_SIZE:100MB}
      # 临时文件存储位置
      location: ${java.io.tmpdir}/naii-upload

# 文件上传配置 - 开发环境特定配置
fileupload:
  domain: http://***********:8080/
  maxSize: 100MB

# VM配置 - 开发环境覆盖
vm:
  showsql: true
wm:
  showsql: true

# NAII 自定义配置 - 开发环境特定配置
naii:
  request:
    frequency:
      url:
        # 开发环境不限制URL访问频率，便于调试
        delay: 0
      ip:
        # 开发环境放宽IP请求限制
        requestNumber: 100
        # 触发禁止访问保护的持续时间（毫秒）- 开发环境缩短为5分钟
      forbidTime: 300000

# 开发环境性能监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,configprops,beans
  endpoint:
    health:
      show-details: always
