# 上传图片404问题修复总结

## 🔍 问题分析

### 1. 主要问题
- **test和prod环境上传图片404**: `No mapping for POST /sites/uploadImageTinymceFile.naii`
- **URL编码问题**: `No mapping for GET /news/%7Bvalue%7D` (其中%7B和%7D是{和}的URL编码)

### 2. 根本原因
1. **静态资源配置差异**:
   - dev环境: `static-path-pattern: /static/**` ✅
   - test环境: `static-path-pattern: /**` ❌ (导致路径冲突)
   - prod环境: `static-path-pattern: /static/**` ✅

2. **控制器路径冲突**:
   - `admin.SiteController`: `@RequestMapping("/sites")`
   - `manager.SiteController`: `@RequestMapping("/sites")`
   - 两个控制器映射到相同路径导致Spring无法正确路由

3. **运行模式检测问题**:
   - `Global.isJarRun`默认为true，但缺少动态检测逻辑
   - 不同环境下路径解析可能不正确

4. **URL模板变量未替换**:
   - 前端代码中存在`{value}`这样的模板变量未被正确替换

## 🛠️ 解决方案

### 1. 修复test环境静态资源配置
**文件**: `src/main/resources/application-test.yml`
```yaml
spring:
  mvc:
    # 测试环境静态资源配置 - 移除view配置避免与Java配置冲突
    static-path-pattern: /static/**
```

### 2. 解决控制器配置冲突
**删除重复的WebMvcConfigurer类**:
- 删除了 `src/main/java/cn/edu/sjtu/gateway/vm/system/WebMvcConfigurer_.java`
- 将ViewResolver配置合并到 `WebConfiguration.java` 中

**添加运行模式自动检测**:
```java
private static void detectRunMode() {
    try {
        String classPath = WebConfiguration.class.getProtectionDomain().getCodeSource().getLocation().toString();
        if (classPath.contains(".jar!") || classPath.endsWith(".jar")) {
            Global.isJarRun = true; // JAR包运行模式
        } else if (classPath.contains("/target/classes/") || classPath.contains("/build/classes/")) {
            Global.isJarRun = false; // 开发模式
        } else if (classPath.contains("/WEB-INF/")) {
            Global.isJarRun = false; // WAR包运行模式
        }
    } catch (Exception e) {
        Global.isJarRun = true; // 默认JAR包模式
    }
}
```

### 3. 添加缺失的上传接口
**文件**: `src/main/java/cn/edu/sjtu/gateway/manager/controller/SiteController.java`

添加了以下上传接口:
- `uploadImageTinymceFile.naii` - TinyMCE文件上传
- `uploadImageTinymce.naii` - TinyMCE图片上传  
- `uploadImage.naii` - 通用图片上传
- `tinymceUploadImage.naii` - 富文本图片上传

### 4. 优化静态资源映射
```java
@Override
public void addResourceHandlers(ResourceHandlerRegistry registry) {
    // 映射上传的文件 - 这是最重要的映射
    registry.addResourceHandler("/uploads/**")
            .addResourceLocations("file:" + basePath + "uploads/")
            .setCachePeriod(3600);
    
    // 映射站点文件
    registry.addResourceHandler("/site/**")
            .addResourceLocations("file:" + basePath + "uploads/site/")
            .setCachePeriod(3600);
}
```

### 5. 修复URL解析问题
**文件**: `src/main/java/cn/edu/sjtu/gateway/manager/controller/SiteController.java`

改进了HTML文件解析逻辑，添加了详细的日志记录:
```java
int newsId = Lang.stringToInt(htmlFile, 0);
if (newsId > 0) {
    redirectUrl = "news/news.naii?id=" + newsId;
    log.info("重定向到文章页面: newsId={}, redirectUrl={}", newsId, redirectUrl);
} else {
    log.warn("无法解析页面ID: htmlFile={}", htmlFile);
    return error(model, "未知页面: " + htmlFile);
}
```

## 🧪 测试工具

### 1. 配置检查器
**文件**: `src/main/java/cn/edu/sjtu/gateway/common/config/StaticResourceChecker.java`
- 启动时自动检查静态资源配置
- 确保uploads目录存在
- 输出详细的配置信息

### 2. URL映射修复器
**文件**: `src/main/java/cn/edu/sjtu/gateway/common/config/UrlMappingFixer.java`
- 检查控制器路径映射
- 提供修复建议
- 验证上传接口映射

### 3. 前端URL修复工具
**文件**: `src/main/resources/static/js/url-fix.js`
- 自动检测和修复URL编码问题
- 修复上传表单action路径
- 拦截AJAX请求并修正URL

### 4. 测试页面
**文件**: `src/main/resources/static/test-upload.html`
- 提供完整的上传功能测试界面
- 测试所有上传接口
- 实时显示测试结果

## 📋 测试步骤

### 1. 重启应用
```bash
# 重启应用以应用所有配置更改
```

### 2. 检查启动日志
查看以下关键日志信息:
- `🔍 开始检查静态资源配置...`
- `当前运行模式: JAR包模式/WAR包模式`
- `✅ 创建上传目录: /path/to/uploads`
- `📁 静态资源映射配置:`

### 3. 访问测试接口
- **配置检查**: `GET /test/static-config`
- **上传目录访问**: `GET /test/upload-access`
- **测试页面**: `GET /static/test-upload.html`

### 4. 测试上传功能
使用测试页面测试以下接口:
- `/sites/uploadImageTinymceFile.naii`
- `/sites/uploadImageTinymce.naii`
- `/sites/uploadImage.naii`
- `/sites/tinymceUploadImage.naii`

## 🎯 预期结果

### 1. 所有环境统一
- dev、test、prod环境的静态资源配置一致
- 上传功能在所有环境下正常工作

### 2. 路径映射正确
- `/uploads/**` 正确映射到文件系统路径
- `/site/**` 正确映射到站点文件路径

### 3. 上传接口可用
- 所有上传接口返回正确的响应
- 上传的文件可以通过URL正常访问

### 4. 错误消除
- 不再出现 `No mapping for POST /sites/uploadImageTinymceFile.naii`
- 不再出现 `No mapping for GET /news/%7Bvalue%7D`

## 🔧 故障排除

### 如果上传仍然404:
1. 检查启动日志中的运行模式检测
2. 验证uploads目录是否存在且有写权限
3. 检查静态资源映射日志输出
4. 使用测试接口验证配置

### 如果URL编码问题仍然存在:
1. 检查前端代码中的模板变量替换
2. 确保url-fix.js正确加载
3. 检查浏览器控制台的错误信息

### 如果路径冲突仍然存在:
1. 确认WebMvcConfigurer_.java已被删除
2. 检查是否有其他重复的@RequestMapping
3. 重启应用以清除缓存

## 📝 维护建议

1. **定期检查**: 使用测试页面定期验证上传功能
2. **监控日志**: 关注启动时的配置检查日志
3. **环境一致性**: 确保所有环境的配置文件保持一致
4. **文档更新**: 当添加新的上传接口时，更新相关文档

---

**修复完成时间**: 2025-08-01  
**修复状态**: ✅ 已完成  
**测试状态**: 🧪 待验证
